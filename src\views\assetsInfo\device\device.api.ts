import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/biz/device/queryPage',
  save = '/biz/device/add',
  edit = '/biz/device/edit',
  delete = '/biz/device/delete',
  deleteBatch = '/mock/device/deleteBatch',
  importExcel = '/mock/device/importExcel',
  exportXls = '/mock/device/exportXls',
  exportAll = '/mock/device/exportAll',
  downloadTemplate = '/mock/device/downloadTemplate',
  detail = '/biz/device/detail/',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail + id, params: { id } });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteDevice = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteDevice = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 导入
 * @param params
 */
export const importExcel = (params) => defHttp.post({ url: Api.importExcel, params });

/**
 * 导出
 * @param params
 */
export const exportDevice = (params) => defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });

/**
 * 全部导出
 * @param params
 */
export const exportAllDevice = (params) => defHttp.get({ url: Api.exportAll, params, responseType: 'blob' });

/**
 * 下载导入模板
 */
export const downloadTemplate = () => defHttp.get({ url: Api.downloadTemplate, responseType: 'blob' });